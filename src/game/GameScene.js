import Phaser from 'phaser'
import MenuScene from './MenuScene'
import GameLogic from './GameLogic'
import CardManager from './CardManager'

class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' })
    this.gameLogic = new GameLogic()
    this.cardManager = null
    this.gameState = null
    this.messageText = null
    this.actionButtons = []
    this.playerInfoTexts = []
  }

  preload() {
    console.log('GameScene: preload')

    // 加载背景图片
    this.load.image('table_bg', '/table_bg_1.jpg')
    this.load.image('table_bg_2', '/table_bg_2.jpg')
    this.load.image('bg2', '/bg2.png')
    this.load.image('mahjong_table', '/mahjong_table.jpg')

    // 加载UI按钮
    this.load.image('btn_chupai', '/UI/button/btn_chupai.png')
    this.load.image('btn_bujiao', '/UI/button/btn_bujiao.png')
    this.load.image('btn_tisji', '/UI/button/btn_tisji.png')
    this.load.image('goback', '/UI/button/goback.png')
    this.load.image('qiangzhuang', '/UI/button/qiangzhuang.png')
    this.load.image('buqiangzhuang', '/UI/button/buqiangzhuang.png')

    // 加载叫分按钮 - 使用数字按钮
    this.load.image('join_1', '/UI/join_1.png')
    this.load.image('join_2', '/UI/join_2.png')
    this.load.image('join_3', '/UI/join_3.png')

    // 加载其他UI元素
    this.load.image('landlord_icon', '/UI/headimage/img_Card_dizhu.png')
    this.load.image('frame_vip', '/UI/frame_vip.png')
    this.load.image('room_money_frame', '/UI/room_money_frame.png')

    // 加载卡牌纹理
    this.load.atlas('cards', '/UI/card/card.png', '/UI/card/card.plist')

    // 加载音效
    this.load.audio('fapai', '/sound/fapai.mp3')
    this.load.audio('chupai', '/sound/chupai.mp3')
    this.load.audio('bg_music', '/sound/bg.mp3')
    this.load.audio('woman_jiao_di_zhu', '/sound/woman_jiao_di_zhu.ogg')
    this.load.audio('woman_bu_jiao', '/sound/woman_bu_jiao.ogg')
  }

  create() {
    console.log('GameScene: create')

    // 设置背景色 - 深绿色牌桌
    this.cameras.main.setBackgroundColor('#0d4f1c')

    this.cardManager = new CardManager(this)

    this.createGameTable()
    this.createPlayerAreas()
    this.createGameUI()
    this.createActionButtons()

    // 监听卡牌选择变化
    this.events.on('cardsSelectionChanged', this.onCardsSelectionChanged, this)

    // 开始游戏
    this.startNewGame()
  }

  createGameTable() {
    // 使用更好的背景图片组合
    const mainBg = this.add.image(400, 300, 'bg2')
    mainBg.setDisplaySize(800, 600)

    // 添加牌桌背景
    const tableBg = this.add.image(400, 300, 'mahjong_table')
    tableBg.setDisplaySize(700, 500)
    tableBg.setAlpha(0.8)

    // 添加装饰性边框
    const borderGraphics = this.add.graphics()
    borderGraphics.lineStyle(4, 0xFFD700, 0.8)
    borderGraphics.strokeRoundedRect(50, 50, 700, 500, 20)
    borderGraphics.lineStyle(2, 0xffffff, 0.6)
    borderGraphics.strokeRoundedRect(52, 52, 696, 496, 18)

    // 添加中央装饰圆形区域
    const centerDecor = this.add.graphics()
    centerDecor.fillStyle(0x000000, 0.2)
    centerDecor.fillCircle(400, 300, 120)
    centerDecor.lineStyle(3, 0xFFD700, 0.7)
    centerDecor.strokeCircle(400, 300, 120)
    centerDecor.lineStyle(1, 0xffffff, 0.5)
    centerDecor.strokeCircle(400, 300, 115)

    // 添加游戏标题 - 使用更好的样式
    const titleBg = this.add.graphics()
    titleBg.fillStyle(0x000000, 0.7)
    titleBg.fillRoundedRect(300, 20, 200, 60, 15)
    titleBg.lineStyle(2, 0xFFD700, 0.8)
    titleBg.strokeRoundedRect(300, 20, 200, 60, 15)

    this.add.text(400, 50, '斗地主', {
      fontSize: '32px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 3
    }).setOrigin(0.5)
  }

  createPlayerAreas() {
    // 玩家位置标识 - 重新调整位置以适应卡牌布局
    const playerPositions = [
      { x: 400, y: 550, name: '玩家（南）', color: '#4CAF50', id: 0 }, // 下方 - 当前玩家
      { x: 50, y: 120, name: '玩家（西）', color: '#FF9800', id: 1 }, // 左侧 - 调整位置
      { x: 750, y: 120, name: '玩家（东）', color: '#2196F3', id: 2 }  // 右侧 - 调整位置
    ]

    this.playerInfoTexts = []

    playerPositions.forEach((playerPos) => {
      // 根据玩家位置调整信息框大小和位置
      let boxWidth = 140
      let boxHeight = 40
      let boxX = playerPos.x - boxWidth/2
      let boxY = playerPos.y - boxHeight/2

      // 对于左右两侧的玩家，调整信息框位置避免与卡牌重叠
      if (playerPos.id === 1) { // 左侧玩家
        boxX = 10
        boxY = playerPos.y - 20
        boxWidth = 80
      } else if (playerPos.id === 2) { // 右侧玩家
        boxX = 710
        boxY = playerPos.y - 20
        boxWidth = 80
      }

      // 玩家区域背景
      const graphics = this.add.graphics()
      graphics.fillStyle(parseInt(playerPos.color.replace('#', '0x')), 0.9)
      graphics.fillRoundedRect(boxX, boxY, boxWidth, boxHeight, 8)
      graphics.lineStyle(2, 0xffffff, 0.8)
      graphics.strokeRoundedRect(boxX, boxY, boxWidth, boxHeight, 8)

      // 玩家名称
      const nameText = this.add.text(boxX + boxWidth/2, boxY + 12, playerPos.name, {
        fontSize: '12px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 2
      }).setOrigin(0.5)

      // 手牌数量和状态显示
      const infoText = this.add.text(boxX + boxWidth/2, boxY + 28, '手牌: 17张', {
        fontSize: '10px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        stroke: '#000000',
        strokeThickness: 1
      }).setOrigin(0.5)

      this.playerInfoTexts[playerPos.id] = {
        name: nameText,
        info: infoText,
        position: playerPos,
        background: graphics
      }
    })
  }

  createGameUI() {
    // 游戏信息面板
    const infoPanel = this.add.graphics()
    infoPanel.fillStyle(0x333333)
    infoPanel.fillRoundedRect(20, 20, 250, 80, 5)

    this.gameInfoText = this.add.text(30, 30, '当前局数: 1/3', {
      fontSize: '14px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    })

    this.scoreInfoText = this.add.text(30, 50, '底分: 1分', {
      fontSize: '14px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif'
    })

    this.phaseInfoText = this.add.text(30, 70, '阶段: 发牌中', {
      fontSize: '14px',
      color: '#ffff00',
      fontFamily: 'Arial, sans-serif'
    })

    // 返回菜单按钮 - 使用图片按钮
    const backBtn = this.add.image(750, 50, 'goback')
    backBtn.setScale(0.6)
    backBtn.setOrigin(1, 0)
    backBtn.setInteractive()

    backBtn.on('pointerdown', () => {
      this.scene.start('MenuScene')
    })

    backBtn.on('pointerover', () => {
      backBtn.setScale(0.65)
    })

    backBtn.on('pointerout', () => {
      backBtn.setScale(0.6)
    })

    // 中央消息显示区域
    this.messageText = this.add.text(400, 350, '正在发牌...', {
      fontSize: '20px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      padding: { x: 15, y: 8 },
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
  }

  createActionButtons() {
    const buttonY = 500
    this.actionButtons = []

    // 叫地主阶段按钮 - 使用更好的布局和图片资源
    const callButtons = [
      { text: '1分', x: 280, action: 'call1', phase: 'calling', image: 'join_1' },
      { text: '2分', x: 350, action: 'call2', phase: 'calling', image: 'join_2' },
      { text: '3分', x: 420, action: 'call3', phase: 'calling', image: 'join_3' },
      { text: '不叫', x: 520, action: 'pass', phase: 'calling', image: 'buqiangzhuang' }
    ]

    // 出牌阶段按钮
    const playButtons = [
      { text: '出牌', x: 320, action: 'playCards', phase: 'playing', image: 'btn_chupai' },
      { text: '不要', x: 400, action: 'skip', phase: 'playing', image: 'btn_bujiao' },
      { text: '提示', x: 480, action: 'hint', phase: 'playing', image: 'btn_tisji' }
    ]

    const allButtons = [...callButtons, ...playButtons]

    // 创建按钮背景区域
    const buttonAreaBg = this.add.graphics()
    buttonAreaBg.fillStyle(0x000000, 0.4)
    buttonAreaBg.fillRoundedRect(200, buttonY - 40, 400, 80, 15)
    buttonAreaBg.lineStyle(2, 0xFFD700, 0.6)
    buttonAreaBg.strokeRoundedRect(200, buttonY - 40, 400, 80, 15)

    allButtons.forEach(btn => {
      let button

      if (btn.image) {
        // 使用图片按钮
        button = this.add.image(btn.x, buttonY, btn.image)
        button.setScale(0.7)
        button.setInteractive()

        // 添加按钮发光效果
        button.setTint(0xffffff)

        // 为叫分按钮添加文字标签
        if (btn.phase === 'calling' && btn.text !== '不叫') {
          const label = this.add.text(btn.x, buttonY + 35, btn.text, {
            fontSize: '14px',
            color: '#FFD700',
            fontFamily: 'Arial, sans-serif',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
          }).setOrigin(0.5)
          label.setVisible(false)
          button.label = label
        }
      } else {
        // 备用文字按钮（如果图片加载失败）
        button = this.add.text(btn.x, buttonY, btn.text, {
          fontSize: '16px',
          color: '#ffffff',
          backgroundColor: 'rgba(212, 175, 55, 0.9)',
          padding: { x: 15, y: 8 },
          fontFamily: 'Arial, sans-serif',
          fontStyle: 'bold'
        }).setOrigin(0.5)
        button.setInteractive()
      }

      button.setVisible(false) // 初始隐藏

      button.on('pointerdown', () => {
        this.handleButtonClick(btn.action)
        // 播放点击音效
        if (this.sound.get('chupai')) {
          this.sound.play('chupai', { volume: 0.5 })
        }

        // 点击动画效果
        this.tweens.add({
          targets: button,
          scaleX: 0.6,
          scaleY: 0.6,
          duration: 100,
          yoyo: true,
          ease: 'Power2'
        })
      })

      // 鼠标悬停效果
      button.on('pointerover', () => {
        if (button.visible) {
          if (btn.image) {
            button.setScale(0.75)
            button.setTint(0xffff99) // 悬停时变亮
          } else {
            button.setScale(1.05)
          }
        }
      })

      button.on('pointerout', () => {
        if (button.visible) {
          if (btn.image) {
            button.setScale(0.7)
            button.setTint(0xffffff) // 恢复原色
          } else {
            button.setScale(1)
          }
        }
      })

      button.buttonData = btn
      this.actionButtons.push(button)
    })
  }

  // 开始新游戏
  startNewGame() {
    this.showMessage('正在发牌...', 2000)

    // 播放发牌音效
    if (this.sound.get('fapai')) {
      this.sound.play('fapai', { volume: 0.6 })
    }

    // 延迟发牌，增加真实感
    this.time.delayedCall(1000, () => {
      this.gameLogic.dealCards()
      this.gameState = this.gameLogic.getGameState()

      this.updateDisplay()
      this.showMessage('发牌完成，开始叫地主', 2000)
    })
  }

  // 更新显示
  updateDisplay() {
    if (!this.gameState) return

    // 更新游戏信息
    this.phaseInfoText.setText(`阶段: ${this.getPhaseText()}`)
    this.scoreInfoText.setText(`底分: ${this.gameState.baseScore}分 倍数: ${this.gameState.multiplier}`)

    // 更新玩家信息
    this.gameState.players.forEach((player, index) => {
      if (this.playerInfoTexts[index]) {
        const landlordMark = player.isLandlord ? ' 👑' : ''
        const currentMark = (this.gameState.currentPlayer === index) ? ' ⭐' : ''

        this.playerInfoTexts[index].name.setText(player.name + landlordMark + currentMark)
        this.playerInfoTexts[index].info.setText(`手牌: ${player.cards.length}张`)
      }
    })

    // 显示卡牌
    this.displayCards()

    // 更新按钮状态
    this.updateButtons()
  }

  // 显示卡牌
  displayCards() {
    if (!this.gameState) return

    // 显示当前玩家（南）的手牌
    const currentPlayer = this.gameState.players[0]
    this.cardManager.displayPlayerCards(currentPlayer, 400, 450, true)

    // 显示其他玩家的手牌背面 - 调整位置和方向
    this.cardManager.displayOtherPlayerCards(this.gameState.players[1], 50, 250, true) // 西 - 垂直排列
    this.cardManager.displayOtherPlayerCards(this.gameState.players[2], 750, 250, true) // 东 - 垂直排列

    // 显示出牌区域
    if (this.gameState.lastPlayedCards) {
      this.cardManager.displayPlayedCards(this.gameState.lastPlayedCards, 400, 320)
    }

    // 显示地主牌
    if (this.gameState.landlordId !== -1 && this.gameState.gamePhase === 'playing') {
      this.cardManager.displayLandlordCards(this.gameLogic.landlordCards, 400, 120)
    }
  }

  // 获取阶段文本
  getPhaseText() {
    switch(this.gameState?.gamePhase) {
      case 'dealing': return '发牌中'
      case 'calling': return '叫地主'
      case 'playing': return '出牌中'
      case 'ended': return '游戏结束'
      default: return '未知'
    }
  }

  // 更新按钮状态
  updateButtons() {
    if (!this.gameState) return

    // 隐藏所有按钮和标签
    this.actionButtons.forEach(button => {
      button.setVisible(false)
      if (button.label) {
        button.label.setVisible(false)
      }
    })

    // 只有当前玩家是人类玩家（ID=0）时才显示按钮
    if (this.gameState.currentPlayer !== 0) {
      // 如果不是人类玩家的回合，启动AI操作
      if (this.gameState.gamePhase === 'calling') {
        this.time.delayedCall(1500, () => {
          this.aiCall()
        })
      } else if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(1500, () => {
          this.aiPlay()
        })
      }
      return
    }

    // 根据游戏阶段显示对应按钮
    this.actionButtons.forEach(button => {
      if (button.buttonData.phase === this.gameState.gamePhase) {
        button.setVisible(true)
        if (button.label) {
          button.label.setVisible(true)
        }
      }
    })
  }

  // 处理按钮点击
  handleButtonClick(action) {
    if (!this.gameState) return

    switch(action) {
      case 'call1':
        this.handleCall(1)
        break
      case 'call2':
        this.handleCall(2)
        break
      case 'call3':
        this.handleCall(3)
        break
      case 'pass':
        this.handleCall(0)
        break
      case 'playCards':
        this.handlePlayCards()
        break
      case 'skip':
        this.handleSkip()
        break
      case 'hint':
        this.showHint()
        break
    }
  }

  // 处理叫地主
  handleCall(points) {
    const result = this.gameLogic.callLandlord(0, points) // 玩家ID=0

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      // 如果游戏进入出牌阶段，清除中央消息
      if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(2000, () => {
          this.messageText.setText('请出牌')
        })
      }
      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // AI叫地主
  aiCall() {
    if (this.gameState.gamePhase !== 'calling' || this.gameState.currentPlayer === 0) return

    // 简单AI逻辑：随机决定是否叫地主
    const aiPlayer = this.gameState.currentPlayer
    const shouldCall = Math.random() > 0.7 // 30%概率叫地主
    const points = shouldCall ? Math.floor(Math.random() * 3) + 1 : 0

    const result = this.gameLogic.callLandlord(aiPlayer, points)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(2000, () => {
          this.messageText.setText('请出牌')
        })
      }
      // 注意：不再在这里递归调用aiCall，而是通过updateButtons来处理下一个玩家
    }
  }

  // 处理出牌
  handlePlayCards() {
    const selectedCards = this.cardManager.getSelectedCards()

    if (selectedCards.length === 0) {
      this.showMessage('请先选择要出的牌', 2000)
      return
    }

    const result = this.gameLogic.playCards(0, selectedCards)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.cardManager.clearSelection()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      if (this.gameState.gamePhase === 'ended') {
        this.handleGameEnd(result)
      }
      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // 处理跳过
  handleSkip() {
    const result = this.gameLogic.skipTurn(0)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // AI出牌
  aiPlay() {
    if (this.gameState.gamePhase !== 'playing' || this.gameState.currentPlayer === 0) return

    const aiPlayer = this.gameState.players[this.gameState.currentPlayer]

    // 简单AI：随机选择是否出牌
    const shouldPlay = Math.random() > 0.3 // 70%概率出牌

    if (shouldPlay && aiPlayer.cards.length > 0) {
      // 简单出牌：出最小的单张
      const smallestCard = aiPlayer.cards[0]
      const result = this.gameLogic.playCards(this.gameState.currentPlayer, [smallestCard])

      if (result.success) {
        this.gameState = this.gameLogic.getGameState()
        this.showMessage(result.message, 2000)
        this.updateDisplay()

        if (this.gameState.gamePhase === 'ended') {
          this.handleGameEnd(result)
          return
        }
      }
    } else {
      // 跳过
      const result = this.gameLogic.skipTurn(this.gameState.currentPlayer)
      if (result.success) {
        this.gameState = this.gameLogic.getGameState()
        this.showMessage(result.message, 2000)
        this.updateDisplay()
      }
    }

    // AI调用现在由updateDisplay -> updateButtons统一管理
  }

  // 显示提示
  showHint() {
    const selectedCards = this.cardManager.getSelectedCards()
    if (selectedCards.length === 0) {
      this.showMessage('请先选择卡牌查看提示', 2000)
      return
    }

    const cardType = this.gameLogic.validateCardType(selectedCards)
    if (cardType) {
      this.showMessage(`牌型: ${cardType.type}, 大小: ${cardType.value}`, 3000)
    } else {
      this.showMessage('无效的牌型组合', 2000)
    }
  }

  // 处理游戏结束
  handleGameEnd(result) {
    const winMessage = result.isLandlordWin ? '地主获胜！' : '农民获胜！'
    this.showMessage(winMessage, 5000)

    // 5秒后重新开始游戏
    this.time.delayedCall(5000, () => {
      this.restartGame()
    })
  }

  // 重新开始游戏
  restartGame() {
    this.cardManager.destroy()
    this.cardManager = new CardManager(this)
    this.gameLogic.reset()
    this.startNewGame()
  }

  // 卡牌选择变化处理
  onCardsSelectionChanged(selectedCards) {
    // 可以在这里添加选中卡牌的视觉反馈
    console.log('选中卡牌数量:', selectedCards.length)
  }

  // 显示消息
  showMessage(text, duration = 3000) {
    if (this.messageText) {
      this.messageText.setText(text)

      // 如果有持续时间，设置定时清除
      if (duration > 0) {
        this.time.delayedCall(duration, () => {
          if (this.messageText) {
            this.messageText.setText('')
          }
        })
      }
    }
  }

  // 场景销毁时清理
  destroy() {
    if (this.cardManager) {
      this.cardManager.destroy()
    }
    super.destroy()
  }
}

// 游戏初始化函数
export default function initGame() {
  const container = document.getElementById('game-container')
  if (!container) {
    throw new Error('Game container element not found')
  }

  const config = {
    type: Phaser.AUTO,
    parent: 'game-container',
    width: 800,
    height: 600,
    scene: [MenuScene, GameScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0 },
        debug: false
      }
    }
  }

  const game = new Phaser.Game(config)
  return game
}

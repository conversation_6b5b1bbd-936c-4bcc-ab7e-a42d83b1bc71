import Phaser from 'phaser'
import MenuScene from './MenuScene'
import GameLogic from './GameLogic'
import CardManager from './CardManager'

class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' })
    this.gameLogic = new GameLogic()
    this.cardManager = null
    this.gameState = null
    this.messageText = null
    this.actionButtons = []
    this.playerInfoTexts = []
  }

  preload() {
    console.log('GameScene: preload')

    // 加载背景图片
    this.load.image('table_bg', '/table_bg_1.jpg')
    this.load.image('table_bg_2', '/table_bg_2.jpg')
    this.load.image('bg2', '/bg2.png')
    this.load.image('mahjong_table', '/mahjong_table.jpg')

    // 加载UI按钮
    this.load.image('btn_chupai', '/UI/button/btn_chupai.png')
    this.load.image('btn_bujiao', '/UI/button/btn_bujiao.png')
    this.load.image('btn_tisji', '/UI/button/btn_tisji.png')
    this.load.image('goback', '/UI/button/goback.png')
    this.load.image('qiangzhuang', '/UI/button/qiangzhuang.png')
    this.load.image('buqiangzhuang', '/UI/button/buqiangzhuang.png')

    // 加载叫分按钮 - 使用数字按钮
    this.load.image('join_1', '/UI/join_1.png')
    this.load.image('join_2', '/UI/join_2.png')
    this.load.image('join_3', '/UI/join_3.png')

    // 加载其他UI元素
    this.load.image('landlord_icon', '/UI/headimage/img_Card_dizhu.png')
    this.load.image('frame_vip', '/UI/frame_vip.png')
    this.load.image('room_money_frame', '/UI/room_money_frame.png')

    // 加载卡牌纹理
    this.load.atlas('cards', '/UI/card/card.png', '/UI/card/card.plist')

    // 加载音效
    this.load.audio('fapai', '/sound/fapai.mp3')
    this.load.audio('chupai', '/sound/chupai.mp3')
    this.load.audio('bg_music', '/sound/bg.mp3')
    this.load.audio('woman_jiao_di_zhu', '/sound/woman_jiao_di_zhu.ogg')
    this.load.audio('woman_bu_jiao', '/sound/woman_bu_jiao.ogg')
  }

  create() {
    console.log('GameScene: create')

    // 设置背景色 - 深绿色牌桌
    this.cameras.main.setBackgroundColor('#0d4f1c')

    this.cardManager = new CardManager(this)

    this.createGameTable()
    this.createPlayerAreas()
    this.createGameUI()
    this.createActionButtons()

    // 监听卡牌选择变化
    this.events.on('cardsSelectionChanged', this.onCardsSelectionChanged, this)

    // 开始游戏
    this.startNewGame()
  }

  createGameTable() {
    // 主背景
    const mainBg = this.add.image(400, 300, 'bg2')
    mainBg.setDisplaySize(800, 600)

    // 游戏桌面背景
    const tableBg = this.add.image(400, 300, 'mahjong_table')
    tableBg.setDisplaySize(650, 450)
    tableBg.setAlpha(0.9)

    // 桌面边框
    const tableFrame = this.add.graphics()
    tableFrame.lineStyle(3, 0x8B4513, 1) // 棕色边框
    tableFrame.strokeRoundedRect(75, 75, 650, 450, 15)
    tableFrame.lineStyle(1, 0xFFD700, 0.8) // 金色内边框
    tableFrame.strokeRoundedRect(77, 77, 646, 446, 13)

    // 中央出牌区域标识
    const centerArea = this.add.graphics()
    centerArea.fillStyle(0x000000, 0.1)
    centerArea.fillEllipse(400, 280, 300, 150)
    centerArea.lineStyle(2, 0xFFD700, 0.6)
    centerArea.strokeEllipse(400, 280, 300, 150)

    // 添加中央区域文字
    this.add.text(400, 280, '出牌区域', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 2,
      alpha: 0.7
    }).setOrigin(0.5)

    // 游戏标题
    const titleBg = this.add.graphics()
    titleBg.fillStyle(0x000000, 0.8)
    titleBg.fillRoundedRect(320, 15, 160, 50, 12)
    titleBg.lineStyle(2, 0xFFD700, 1)
    titleBg.strokeRoundedRect(320, 15, 160, 50, 12)

    this.add.text(400, 40, '斗地主', {
      fontSize: '24px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加游戏桌面的四个角落装饰
    this.createCornerDecorations()

    // 添加游戏状态指示器
    this.createGameStatusIndicators()
  }

  createCornerDecorations() {
    const corners = [
      { x: 100, y: 100 },
      { x: 700, y: 100 },
      { x: 100, y: 500 },
      { x: 700, y: 500 }
    ]

    corners.forEach(corner => {
      const decoration = this.add.graphics()
      decoration.fillStyle(0xFFD700, 0.6)
      decoration.fillCircle(corner.x, corner.y, 8)
      decoration.lineStyle(2, 0x8B4513, 1)
      decoration.strokeCircle(corner.x, corner.y, 8)
    })
  }

  createGameStatusIndicators() {
    // 创建倍数显示器（右上角）
    this.multiplierDisplay = this.add.graphics()
    this.multiplierDisplay.fillStyle(0x000000, 0.8)
    this.multiplierDisplay.fillRoundedRect(580, 15, 120, 40, 8)
    this.multiplierDisplay.lineStyle(2, 0xFF6666, 1)
    this.multiplierDisplay.strokeRoundedRect(580, 15, 120, 40, 8)

    this.multiplierDisplayText = this.add.text(640, 35, '倍数: x1', {
      fontSize: '14px',
      color: '#FF6666',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)

    // 创建当前玩家指示器（中央下方）
    this.currentPlayerIndicator = this.add.graphics()
    this.currentPlayerIndicator.fillStyle(0x4CAF50, 0.8)
    this.currentPlayerIndicator.fillRoundedRect(350, 380, 100, 25, 12)
    this.currentPlayerIndicator.lineStyle(2, 0xFFD700, 1)
    this.currentPlayerIndicator.strokeRoundedRect(350, 380, 100, 25, 12)

    this.currentPlayerText = this.add.text(400, 392, '轮到你了', {
      fontSize: '12px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)

    // 初始隐藏
    this.currentPlayerIndicator.setVisible(false)
    this.currentPlayerText.setVisible(false)

    // 创建地主牌剩余指示器
    this.landlordCardsIndicator = this.add.text(400, 100, '', {
      fontSize: '12px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      padding: { x: 8, y: 4 }
    }).setOrigin(0.5).setVisible(false)
  }

  createPlayerAreas() {
    // 标准斗地主三人布局
    const playerPositions = [
      { x: 400, y: 520, name: '我', color: '#4CAF50', id: 0, position: 'bottom' }, // 下方 - 当前玩家
      { x: 100, y: 300, name: '左玩家', color: '#FF9800', id: 1, position: 'left' }, // 左侧玩家
      { x: 700, y: 300, name: '右玩家', color: '#2196F3', id: 2, position: 'right' }  // 右侧玩家
    ]

    this.playerInfoTexts = []

    playerPositions.forEach((playerPos) => {
      // 根据玩家位置设计不同的信息框
      let boxWidth, boxHeight, boxX, boxY

      if (playerPos.position === 'bottom') {
        // 底部玩家 - 信息框在手牌上方
        boxWidth = 120
        boxHeight = 35
        boxX = playerPos.x - boxWidth/2
        boxY = playerPos.y - 80
      } else if (playerPos.position === 'left') {
        // 左侧玩家 - 信息框在左上角
        boxWidth = 100
        boxHeight = 50
        boxX = 20
        boxY = 80
      } else { // right
        // 右侧玩家 - 信息框在右上角
        boxWidth = 100
        boxHeight = 50
        boxX = 680
        boxY = 80
      }

      // 玩家信息背景
      const graphics = this.add.graphics()
      graphics.fillStyle(parseInt(playerPos.color.replace('#', '0x')), 0.85)
      graphics.fillRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)
      graphics.lineStyle(2, 0xffffff, 0.9)
      graphics.strokeRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)

      // 添加内阴影效果
      graphics.lineStyle(1, 0x000000, 0.3)
      graphics.strokeRoundedRect(boxX + 1, boxY + 1, boxWidth - 2, boxHeight - 2, 9)

      // 玩家名称
      const nameText = this.add.text(boxX + boxWidth/2, boxY + 15, playerPos.name, {
        fontSize: '14px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 2
      }).setOrigin(0.5)

      // 手牌数量显示
      const infoText = this.add.text(boxX + boxWidth/2, boxY + 32, '17张', {
        fontSize: '12px',
        color: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        stroke: '#000000',
        strokeThickness: 1
      }).setOrigin(0.5)

      // 地主标识（初始隐藏）
      const landlordIcon = this.add.text(boxX + boxWidth - 15, boxY + 10, '👑', {
        fontSize: '16px'
      }).setOrigin(0.5).setVisible(false)

      this.playerInfoTexts[playerPos.id] = {
        name: nameText,
        info: infoText,
        position: playerPos,
        background: graphics,
        landlordIcon: landlordIcon
      }
    })
  }

  createGameUI() {
    // 左上角游戏信息面板 - 重新设计
    const infoPanel = this.add.graphics()
    infoPanel.fillStyle(0x000000, 0.8)
    infoPanel.fillRoundedRect(15, 75, 200, 90, 10)
    infoPanel.lineStyle(2, 0xFFD700, 0.8)
    infoPanel.strokeRoundedRect(15, 75, 200, 90, 10)

    // 游戏信息文字
    this.gameInfoText = this.add.text(25, 85, '当前局数: 1/3', {
      fontSize: '13px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    this.scoreInfoText = this.add.text(25, 105, '底分: 1分', {
      fontSize: '13px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    this.phaseInfoText = this.add.text(25, 125, '阶段: 发牌中', {
      fontSize: '13px',
      color: '#00ff00',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    this.multiplierText = this.add.text(25, 145, '倍数: x1', {
      fontSize: '13px',
      color: '#ff6666',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    // 右上角返回按钮
    const backBtn = this.add.image(770, 30, 'goback')
    backBtn.setScale(0.5)
    backBtn.setOrigin(1, 0)
    backBtn.setInteractive()

    backBtn.on('pointerdown', () => {
      this.scene.start('MenuScene')
    })

    backBtn.on('pointerover', () => {
      backBtn.setScale(0.55)
      backBtn.setTint(0xffff99)
    })

    backBtn.on('pointerout', () => {
      backBtn.setScale(0.5)
      backBtn.clearTint()
    })

    // 中央消息显示区域 - 重新设计
    this.messageText = this.add.text(400, 200, '正在发牌...', {
      fontSize: '18px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      padding: { x: 20, y: 10 },
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加消息背景装饰
    this.messageBackground = this.add.graphics()
    this.messageBackground.fillStyle(0x000000, 0.6)
    this.messageBackground.lineStyle(2, 0xFFD700, 0.8)
    this.updateMessageBackground()
  }

  // 更新消息背景
  updateMessageBackground() {
    if (this.messageBackground && this.messageText) {
      this.messageBackground.clear()
      const bounds = this.messageText.getBounds()
      this.messageBackground.fillStyle(0x000000, 0.6)
      this.messageBackground.fillRoundedRect(bounds.x - 10, bounds.y - 5, bounds.width + 20, bounds.height + 10, 8)
      this.messageBackground.lineStyle(2, 0xFFD700, 0.8)
      this.messageBackground.strokeRoundedRect(bounds.x - 10, bounds.y - 5, bounds.width + 20, bounds.height + 10, 8)
    }
  }

  createActionButtons() {
    const buttonY = 550 // 移到更底部
    this.actionButtons = []

    // 叫地主阶段按钮 - 居中排列
    const callButtons = [
      { text: '1分', x: 300, action: 'call1', phase: 'calling', image: 'join_1' },
      { text: '2分', x: 370, action: 'call2', phase: 'calling', image: 'join_2' },
      { text: '3分', x: 440, action: 'call3', phase: 'calling', image: 'join_3' },
      { text: '不叫', x: 510, action: 'pass', phase: 'calling', image: 'buqiangzhuang' }
    ]

    // 出牌阶段按钮 - 居中排列
    const playButtons = [
      { text: '出牌', x: 320, action: 'playCards', phase: 'playing', image: 'btn_chupai' },
      { text: '不要', x: 400, action: 'skip', phase: 'playing', image: 'btn_bujiao' },
      { text: '提示', x: 480, action: 'hint', phase: 'playing', image: 'btn_tisji' }
    ]

    const allButtons = [...callButtons, ...playButtons]

    // 创建按钮背景区域 - 调整位置和大小
    const buttonAreaBg = this.add.graphics()
    buttonAreaBg.fillStyle(0x000000, 0.5)
    buttonAreaBg.fillRoundedRect(250, buttonY - 35, 320, 70, 12)
    buttonAreaBg.lineStyle(2, 0xFFD700, 0.7)
    buttonAreaBg.strokeRoundedRect(250, buttonY - 35, 320, 70, 12)

    allButtons.forEach(btn => {
      let button

      if (btn.image) {
        // 使用图片按钮
        button = this.add.image(btn.x, buttonY, btn.image)
        button.setScale(0.6)
        button.setInteractive()

        // 添加按钮阴影效果
        const shadow = this.add.image(btn.x + 2, buttonY + 2, btn.image)
        shadow.setScale(0.6)
        shadow.setTint(0x000000)
        shadow.setAlpha(0.3)
        shadow.setDepth(-1)
        button.shadow = shadow

        // 为叫分按钮添加文字标签
        if (btn.phase === 'calling' && btn.text !== '不叫') {
          const labelBg = this.add.graphics()
          labelBg.fillStyle(0x000000, 0.7)
          labelBg.fillRoundedRect(btn.x - 20, buttonY + 25, 40, 20, 8)
          labelBg.lineStyle(1, 0xFFD700, 0.8)
          labelBg.strokeRoundedRect(btn.x - 20, buttonY + 25, 40, 20, 8)

          const label = this.add.text(btn.x, buttonY + 35, btn.text, {
            fontSize: '12px',
            color: '#FFD700',
            fontFamily: 'Arial, sans-serif',
            fontStyle: 'bold'
          }).setOrigin(0.5)

          labelBg.setVisible(false)
          label.setVisible(false)
          button.labelBg = labelBg
          button.label = label
        }
      } else {
        // 备用文字按钮（如果图片加载失败）
        const buttonBg = this.add.graphics()
        buttonBg.fillStyle(0xFFD700, 0.9)
        buttonBg.fillRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)
        buttonBg.lineStyle(2, 0x8B4513, 1)
        buttonBg.strokeRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)

        button = this.add.text(btn.x, buttonY, btn.text, {
          fontSize: '14px',
          color: '#000000',
          fontFamily: 'Arial, sans-serif',
          fontStyle: 'bold'
        }).setOrigin(0.5)

        button.setInteractive()
        button.background = buttonBg
      }

      button.setVisible(false) // 初始隐藏

      button.on('pointerdown', () => {
        this.handleButtonClick(btn.action)
        // 播放点击音效
        if (this.sound.get('chupai')) {
          this.sound.play('chupai', { volume: 0.5 })
        }

        // 点击动画效果
        this.tweens.add({
          targets: button,
          scaleX: 0.5,
          scaleY: 0.5,
          duration: 80,
          yoyo: true,
          ease: 'Power2'
        })
      })

      // 鼠标悬停效果
      button.on('pointerover', () => {
        if (button.visible) {
          if (btn.image) {
            button.setScale(0.65)
            button.setTint(0xffffcc) // 悬停时变亮
          } else {
            button.setScale(1.1)
            if (button.background) {
              button.background.clear()
              button.background.fillStyle(0xFFFF99, 0.9)
              button.background.fillRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)
              button.background.lineStyle(2, 0x8B4513, 1)
              button.background.strokeRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)
            }
          }
        }
      })

      button.on('pointerout', () => {
        if (button.visible) {
          if (btn.image) {
            button.setScale(0.6)
            button.clearTint() // 恢复原色
          } else {
            button.setScale(1)
            if (button.background) {
              button.background.clear()
              button.background.fillStyle(0xFFD700, 0.9)
              button.background.fillRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)
              button.background.lineStyle(2, 0x8B4513, 1)
              button.background.strokeRoundedRect(btn.x - 30, buttonY - 15, 60, 30, 8)
            }
          }
        }
      })

      button.buttonData = btn
      this.actionButtons.push(button)
    })
  }

  // 开始新游戏
  startNewGame() {
    this.showMessage('正在发牌...', 2000)

    // 播放发牌音效
    if (this.sound.get('fapai')) {
      this.sound.play('fapai', { volume: 0.6 })
    }

    // 添加发牌动画效果
    this.createDealingAnimation()

    // 延迟发牌，增加真实感
    this.time.delayedCall(1000, () => {
      this.gameLogic.dealCards()
      this.gameState = this.gameLogic.getGameState()

      this.updateDisplay()
      this.showMessage('发牌完成，开始叫地主', 2000)
    })
  }

  // 创建发牌动画
  createDealingAnimation() {
    // 创建一些飞行的卡牌动画来模拟发牌过程
    for (let i = 0; i < 6; i++) {
      this.time.delayedCall(i * 100, () => {
        const card = this.add.graphics()
        card.fillStyle(0x0066cc)
        card.fillRoundedRect(-25, -35, 50, 70, 6)
        card.lineStyle(2, 0x003366)
        card.strokeRoundedRect(-25, -35, 50, 70, 6)
        card.x = 400
        card.y = 200

        // 随机飞向三个玩家位置
        const targets = [
          { x: 400, y: 480 }, // 底部玩家
          { x: 60, y: 300 },  // 左侧玩家
          { x: 740, y: 300 }  // 右侧玩家
        ]
        const target = targets[i % 3]

        this.tweens.add({
          targets: card,
          x: target.x,
          y: target.y,
          duration: 500,
          ease: 'Power2',
          onComplete: () => {
            card.destroy()
          }
        })
      })
    }
  }

  // 更新显示
  updateDisplay() {
    if (!this.gameState) return

    // 更新游戏信息
    this.phaseInfoText.setText(`阶段: ${this.getPhaseText()}`)
    this.scoreInfoText.setText(`底分: ${this.gameState.baseScore}分`)
    this.multiplierText.setText(`倍数: x${this.gameState.multiplier}`)

    // 更新倍数显示器
    if (this.multiplierDisplayText) {
      this.multiplierDisplayText.setText(`倍数: x${this.gameState.multiplier}`)
    }

    // 更新当前玩家指示器
    if (this.currentPlayerIndicator && this.currentPlayerText) {
      if (this.gameState.currentPlayer === 0) {
        this.currentPlayerIndicator.setVisible(true)
        this.currentPlayerText.setVisible(true)
        this.currentPlayerText.setText('轮到你了')
      } else {
        this.currentPlayerIndicator.setVisible(false)
        this.currentPlayerText.setVisible(false)
      }
    }

    // 更新地主牌指示器
    if (this.landlordCardsIndicator) {
      if (this.gameState.landlordId !== -1 && this.gameState.gamePhase === 'playing') {
        this.landlordCardsIndicator.setVisible(true)
        this.landlordCardsIndicator.setText(`地主牌 (${this.gameLogic.landlordCards.length}张)`)
      } else {
        this.landlordCardsIndicator.setVisible(false)
      }
    }

    // 更新玩家信息
    this.gameState.players.forEach((player, index) => {
      if (this.playerInfoTexts[index]) {
        // 更新玩家名称，添加当前玩家标识
        const currentMark = (this.gameState.currentPlayer === index) ? ' ⭐' : ''
        this.playerInfoTexts[index].name.setText(player.name + currentMark)

        // 更新手牌数量
        this.playerInfoTexts[index].info.setText(`${player.cards.length}张`)

        // 显示/隐藏地主标识
        if (this.playerInfoTexts[index].landlordIcon) {
          this.playerInfoTexts[index].landlordIcon.setVisible(player.isLandlord)
        }

        // 更新信息框颜色以突出当前玩家
        if (this.gameState.currentPlayer === index) {
          this.playerInfoTexts[index].background.clear()
          this.playerInfoTexts[index].background.fillStyle(0x4CAF50, 1.0) // 更亮的绿色
          const pos = this.playerInfoTexts[index].position
          let boxWidth, boxHeight, boxX, boxY

          if (pos.position === 'bottom') {
            boxWidth = 120; boxHeight = 35
            boxX = pos.x - boxWidth/2; boxY = pos.y - 80
          } else if (pos.position === 'left') {
            boxWidth = 100; boxHeight = 50
            boxX = 20; boxY = 80
          } else {
            boxWidth = 100; boxHeight = 50
            boxX = 680; boxY = 80
          }

          this.playerInfoTexts[index].background.fillRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)
          this.playerInfoTexts[index].background.lineStyle(3, 0xFFD700, 1)
          this.playerInfoTexts[index].background.strokeRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)
        } else {
          // 恢复普通颜色
          this.playerInfoTexts[index].background.clear()
          const color = parseInt(this.playerInfoTexts[index].position.color.replace('#', '0x'))
          this.playerInfoTexts[index].background.fillStyle(color, 0.85)
          const pos = this.playerInfoTexts[index].position
          let boxWidth, boxHeight, boxX, boxY

          if (pos.position === 'bottom') {
            boxWidth = 120; boxHeight = 35
            boxX = pos.x - boxWidth/2; boxY = pos.y - 80
          } else if (pos.position === 'left') {
            boxWidth = 100; boxHeight = 50
            boxX = 20; boxY = 80
          } else {
            boxWidth = 100; boxHeight = 50
            boxX = 680; boxY = 80
          }

          this.playerInfoTexts[index].background.fillRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)
          this.playerInfoTexts[index].background.lineStyle(2, 0xffffff, 0.9)
          this.playerInfoTexts[index].background.strokeRoundedRect(boxX, boxY, boxWidth, boxHeight, 10)
        }
      }
    })

    // 显示卡牌
    this.displayCards()

    // 更新按钮状态
    this.updateButtons()
  }

  // 显示卡牌
  displayCards() {
    if (!this.gameState) return

    // 显示当前玩家（底部）的手牌 - 水平排列
    const currentPlayer = this.gameState.players[0]
    this.cardManager.displayPlayerCards(currentPlayer, 400, 480, true)

    // 显示左侧玩家的手牌背面 - 垂直排列
    this.cardManager.displayOtherPlayerCards(this.gameState.players[1], 60, 300, true, 'left')

    // 显示右侧玩家的手牌背面 - 垂直排列
    this.cardManager.displayOtherPlayerCards(this.gameState.players[2], 740, 300, true, 'right')

    // 显示中央出牌区域
    if (this.gameState.lastPlayedCards) {
      this.cardManager.displayPlayedCards(this.gameState.lastPlayedCards, 400, 280)
    }

    // 显示地主牌（在顶部中央）
    if (this.gameState.landlordId !== -1 && this.gameState.gamePhase === 'playing') {
      this.cardManager.displayLandlordCards(this.gameLogic.landlordCards, 400, 150)
    }
  }

  // 获取阶段文本
  getPhaseText() {
    switch(this.gameState?.gamePhase) {
      case 'dealing': return '发牌中'
      case 'calling': return '叫地主'
      case 'playing': return '出牌中'
      case 'ended': return '游戏结束'
      default: return '未知'
    }
  }

  // 更新按钮状态
  updateButtons() {
    if (!this.gameState) return

    // 隐藏所有按钮和相关元素
    this.actionButtons.forEach(button => {
      button.setVisible(false)
      if (button.shadow) button.shadow.setVisible(false)
      if (button.label) button.label.setVisible(false)
      if (button.labelBg) button.labelBg.setVisible(false)
      if (button.background) button.background.setVisible(false)
    })

    // 只有当前玩家是人类玩家（ID=0）时才显示按钮
    if (this.gameState.currentPlayer !== 0) {
      // 如果不是人类玩家的回合，启动AI操作
      if (this.gameState.gamePhase === 'calling') {
        this.time.delayedCall(1500, () => {
          this.aiCall()
        })
      } else if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(1500, () => {
          this.aiPlay()
        })
      }
      return
    }

    // 根据游戏阶段显示对应按钮
    this.actionButtons.forEach(button => {
      if (button.buttonData.phase === this.gameState.gamePhase) {
        button.setVisible(true)
        if (button.shadow) button.shadow.setVisible(true)
        if (button.label) button.label.setVisible(true)
        if (button.labelBg) button.labelBg.setVisible(true)
        if (button.background) button.background.setVisible(true)
      }
    })
  }

  // 处理按钮点击
  handleButtonClick(action) {
    if (!this.gameState) return

    switch(action) {
      case 'call1':
        this.handleCall(1)
        break
      case 'call2':
        this.handleCall(2)
        break
      case 'call3':
        this.handleCall(3)
        break
      case 'pass':
        this.handleCall(0)
        break
      case 'playCards':
        this.handlePlayCards()
        break
      case 'skip':
        this.handleSkip()
        break
      case 'hint':
        this.showHint()
        break
    }
  }

  // 处理叫地主
  handleCall(points) {
    const result = this.gameLogic.callLandlord(0, points) // 玩家ID=0

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      // 如果游戏进入出牌阶段，清除中央消息
      if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(2000, () => {
          this.messageText.setText('请出牌')
        })
      }
      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // AI叫地主
  aiCall() {
    if (this.gameState.gamePhase !== 'calling' || this.gameState.currentPlayer === 0) return

    // 简单AI逻辑：随机决定是否叫地主
    const aiPlayer = this.gameState.currentPlayer
    const shouldCall = Math.random() > 0.7 // 30%概率叫地主
    const points = shouldCall ? Math.floor(Math.random() * 3) + 1 : 0

    const result = this.gameLogic.callLandlord(aiPlayer, points)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      if (this.gameState.gamePhase === 'playing') {
        this.time.delayedCall(2000, () => {
          this.messageText.setText('请出牌')
        })
      }
      // 注意：不再在这里递归调用aiCall，而是通过updateButtons来处理下一个玩家
    }
  }

  // 处理出牌
  handlePlayCards() {
    const selectedCards = this.cardManager.getSelectedCards()

    if (selectedCards.length === 0) {
      this.showMessage('请先选择要出的牌', 2000)
      return
    }

    const result = this.gameLogic.playCards(0, selectedCards)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.cardManager.clearSelection()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      if (this.gameState.gamePhase === 'ended') {
        this.handleGameEnd(result)
      }
      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // 处理跳过
  handleSkip() {
    const result = this.gameLogic.skipTurn(0)

    if (result.success) {
      this.gameState = this.gameLogic.getGameState()
      this.showMessage(result.message, 2000)
      this.updateDisplay()

      // AI调用现在由updateDisplay -> updateButtons统一管理
    } else {
      this.showMessage(result.message, 2000)
    }
  }

  // AI出牌
  aiPlay() {
    if (this.gameState.gamePhase !== 'playing' || this.gameState.currentPlayer === 0) return

    const aiPlayer = this.gameState.players[this.gameState.currentPlayer]

    // 简单AI：随机选择是否出牌
    const shouldPlay = Math.random() > 0.3 // 70%概率出牌

    if (shouldPlay && aiPlayer.cards.length > 0) {
      // 简单出牌：出最小的单张
      const smallestCard = aiPlayer.cards[0]
      const result = this.gameLogic.playCards(this.gameState.currentPlayer, [smallestCard])

      if (result.success) {
        this.gameState = this.gameLogic.getGameState()
        this.showMessage(result.message, 2000)
        this.updateDisplay()

        if (this.gameState.gamePhase === 'ended') {
          this.handleGameEnd(result)
          return
        }
      }
    } else {
      // 跳过
      const result = this.gameLogic.skipTurn(this.gameState.currentPlayer)
      if (result.success) {
        this.gameState = this.gameLogic.getGameState()
        this.showMessage(result.message, 2000)
        this.updateDisplay()
      }
    }

    // AI调用现在由updateDisplay -> updateButtons统一管理
  }

  // 显示提示
  showHint() {
    const selectedCards = this.cardManager.getSelectedCards()
    if (selectedCards.length === 0) {
      this.showMessage('请先选择卡牌查看提示', 2000)
      return
    }

    const cardType = this.gameLogic.validateCardType(selectedCards)
    if (cardType) {
      this.showMessage(`牌型: ${cardType.type}, 大小: ${cardType.value}`, 3000)
    } else {
      this.showMessage('无效的牌型组合', 2000)
    }
  }

  // 处理游戏结束
  handleGameEnd(result) {
    const winMessage = result.isLandlordWin ? '地主获胜！' : '农民获胜！'
    this.showMessage(winMessage, 5000)

    // 5秒后重新开始游戏
    this.time.delayedCall(5000, () => {
      this.restartGame()
    })
  }

  // 重新开始游戏
  restartGame() {
    this.cardManager.destroy()
    this.cardManager = new CardManager(this)
    this.gameLogic.reset()
    this.startNewGame()
  }

  // 卡牌选择变化处理
  onCardsSelectionChanged(selectedCards) {
    // 可以在这里添加选中卡牌的视觉反馈
    console.log('选中卡牌数量:', selectedCards.length)
  }

  // 显示消息
  showMessage(text, duration = 3000) {
    if (this.messageText) {
      this.messageText.setText(text)
      this.updateMessageBackground()

      // 添加消息动画效果
      this.messageText.setAlpha(0)
      this.tweens.add({
        targets: this.messageText,
        alpha: 1,
        duration: 300,
        ease: 'Power2'
      })

      // 如果有持续时间，设置定时清除
      if (duration > 0) {
        this.time.delayedCall(duration, () => {
          if (this.messageText) {
            this.tweens.add({
              targets: this.messageText,
              alpha: 0,
              duration: 300,
              ease: 'Power2',
              onComplete: () => {
                this.messageText.setText('')
                this.messageText.setAlpha(1)
              }
            })
          }
        })
      }
    }
  }

  // 场景销毁时清理
  destroy() {
    if (this.cardManager) {
      this.cardManager.destroy()
    }
    super.destroy()
  }
}

// 游戏初始化函数
export default function initGame() {
  const container = document.getElementById('game-container')
  if (!container) {
    throw new Error('Game container element not found')
  }

  const config = {
    type: Phaser.AUTO,
    parent: 'game-container',
    width: 800,
    height: 600,
    scene: [MenuScene, GameScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0 },
        debug: false
      }
    }
  }

  const game = new Phaser.Game(config)
  return game
}

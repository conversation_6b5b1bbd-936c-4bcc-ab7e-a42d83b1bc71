// 卡牌管理器
export default class CardManager {
  constructor(scene) {
    this.scene = scene
    this.cardSprites = new Map() // 存储卡牌精灵
    this.selectedCards = [] // 选中的卡牌
    // 调整卡牌尺寸为更合适的大小
    this.cardWidth = 55
    this.cardHeight = 75
  }

  // 获取卡牌图片帧名称
  getCardFrame(card) {
    // 根据plist文件中的命名规则：card_1.png 到 card_54.png
    // 需要将卡牌映射到对应的数字

    if (card.rank === '小王') return 'card_53.png'  // 小王
    if (card.rank === '大王') return 'card_54.png'  // 大王

    // 普通卡牌映射
    const suitOrder = ['♠', '♥', '♣', '♦'] // 黑桃、红心、梅花、方块
    const rankOrder = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

    const suitIndex = suitOrder.indexOf(card.suit)
    const rankIndex = rankOrder.indexOf(card.rank)

    if (suitIndex === -1 || rankIndex === -1) {
      return 'card_1.png' // 默认卡牌
    }

    // 计算卡牌编号：每个花色13张牌
    const cardNumber = suitIndex * 13 + rankIndex + 1

    return `card_${cardNumber}.png`
  }

  // 创建卡牌精灵
  createCard(card, x, y, isClickable = false) {
    const cardSprite = this.scene.add.container(x, y)

    // 创建改进的卡牌背景
    const cardBg = this.scene.add.graphics()

    // 创建渐变背景
    cardBg.fillGradientStyle(0xffffff, 0xffffff, 0xf5f5f5, 0xf5f5f5, 1)
    cardBg.fillRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)

    // 添加主边框
    cardBg.lineStyle(2, 0x333333, 1)
    cardBg.strokeRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)

    // 添加内边框
    cardBg.lineStyle(1, 0x888888, 0.6)
    cardBg.strokeRoundedRect(-this.cardWidth/2 + 2, -this.cardHeight/2 + 2, this.cardWidth - 4, this.cardHeight - 4, 6)

    // 卡牌花色和数字
    const color = (card.suit === '♠' || card.suit === '♣') ? '#000000' : '#ff0000'

    // 左上角花色
    const suitText = this.scene.add.text(-this.cardWidth/2 + 6, -this.cardHeight/2 + 4, card.suit, {
      fontSize: '18px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    // 左上角数字/字母
    const rankText = this.scene.add.text(-this.cardWidth/2 + 6, -this.cardHeight/2 + 18, card.rank, {
      fontSize: card.rank.length > 1 ? '14px' : '16px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    })

    // 中央花色
    const centerSuit = this.scene.add.text(0, 0, card.suit, {
      fontSize: '28px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)

    // 右下角花色（倒置）
    const bottomSuit = this.scene.add.text(this.cardWidth/2 - 6, this.cardHeight/2 - 4, card.suit, {
      fontSize: '18px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(1, 1).setRotation(Math.PI)

    // 右下角数字（倒置）
    const bottomRank = this.scene.add.text(this.cardWidth/2 - 6, this.cardHeight/2 - 18, card.rank, {
      fontSize: card.rank.length > 1 ? '14px' : '16px',
      color: color,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(1, 1).setRotation(Math.PI)

    // 大王小王特殊处理
    if (card.rank === '小王' || card.rank === '大王') {
      suitText.setText('')
      rankText.setText('')
      bottomSuit.setText('')
      bottomRank.setText('')
      centerSuit.setText(card.display)
      centerSuit.setStyle({
        fontSize: '24px',
        color: card.rank === '大王' ? '#ff0000' : '#000000',
        fontStyle: 'bold'
      })
    }

    cardSprite.add([cardBg, suitText, rankText, centerSuit, bottomSuit, bottomRank])
    cardSprite.cardData = card
    cardSprite.isSelected = false

    if (isClickable) {
      cardSprite.setSize(this.cardWidth, this.cardHeight)
      cardSprite.setInteractive()

      cardSprite.on('pointerdown', () => {
        this.toggleCardSelection(cardSprite)
      })

      cardSprite.on('pointerover', () => {
        if (!cardSprite.isSelected) {
          cardSprite.setScale(1.05)
          cardSprite.setTint(0xffffcc)
        }
      })

      cardSprite.on('pointerout', () => {
        if (!cardSprite.isSelected) {
          cardSprite.setScale(1)
          cardSprite.clearTint()
        }
      })
    }

    this.cardSprites.set(card.id, cardSprite)
    return cardSprite
  }

  // 切换卡牌选中状态
  toggleCardSelection(cardSprite) {
    if (cardSprite.isSelected) {
      // 取消选中
      cardSprite.isSelected = false
      cardSprite.y += 10
      cardSprite.setScale(1)
      
      const index = this.selectedCards.findIndex(c => c.id === cardSprite.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      // 选中
      cardSprite.isSelected = true
      cardSprite.y -= 10
      cardSprite.setScale(1.1)
      
      this.selectedCards.push(cardSprite.cardData)
    }
    
    // 触发选中状态变化事件
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 清除所有选中状态
  clearSelection() {
    this.cardSprites.forEach(cardSprite => {
      if (cardSprite.isSelected) {
        cardSprite.isSelected = false
        cardSprite.y += 10
        cardSprite.setScale(1)
      }
    })
    this.selectedCards = []
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 显示玩家手牌
  displayPlayerCards(player, x, y, isCurrentPlayer = false) {
    // 清除之前的卡牌
    this.clearPlayerCards(player.id)

    // 优化卡牌间距计算
    const maxWidth = 550 // 最大宽度
    const minSpacing = 25 // 最小间距
    const maxSpacing = 35 // 最大间距

    let cardSpacing = Math.min(maxSpacing, Math.max(minSpacing, maxWidth / player.cards.length))
    const totalWidth = (player.cards.length - 1) * cardSpacing
    const startX = x - totalWidth / 2

    player.cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y, isCurrentPlayer)
      cardSprite.playerId = player.id

      // 为当前玩家的手牌添加更自然的扇形排列效果
      if (isCurrentPlayer && player.cards.length > 8) {
        const centerIndex = (player.cards.length - 1) / 2
        const distanceFromCenter = index - centerIndex

        // 角度偏移：中间的牌垂直，两边的牌有轻微倾斜
        const maxAngle = 15 // 最大倾斜角度
        const angleOffset = (distanceFromCenter / centerIndex) * maxAngle
        cardSprite.setRotation(angleOffset * Math.PI / 180)

        // 弧形排列：中间的牌稍微向上
        const arcHeight = 8 // 弧形高度
        const arcOffset = -Math.pow(distanceFromCenter / centerIndex, 2) * arcHeight
        cardSprite.y += arcOffset
      }

      // 设置卡牌深度，确保右边的牌在上层
      cardSprite.setDepth(index)
    })
  }

  // 显示其他玩家手牌（背面）
  displayOtherPlayerCards(player, x, y, isVertical = false, position = 'left') {
    this.clearPlayerCards(player.id)

    const cardCount = player.cards.length

    // 根据位置调整卡牌排列
    let spacing, startX, startY, cardWidth, cardHeight

    if (isVertical) {
      // 垂直排列时使用较小的卡牌和间距
      cardWidth = this.cardWidth * 0.7
      cardHeight = this.cardHeight * 0.7
      spacing = 12

      const totalHeight = (cardCount - 1) * spacing
      startY = y - totalHeight / 2
      startX = x

      // 左右两侧的偏移调整
      if (position === 'left') {
        startX = x + cardWidth / 2
      } else if (position === 'right') {
        startX = x - cardWidth / 2
      }
    } else {
      // 水平排列
      cardWidth = this.cardWidth
      cardHeight = this.cardHeight
      spacing = 30

      const totalWidth = (cardCount - 1) * spacing
      startX = x - totalWidth / 2
      startY = y
    }

    for (let i = 0; i < cardCount; i++) {
      const cardX = isVertical ? startX : startX + i * spacing
      const cardY = isVertical ? startY + i * spacing : startY

      const cardSprite = this.scene.add.container(cardX, cardY)

      // 卡牌背面 - 使用更好的蓝色渐变
      const cardBg = this.scene.add.graphics()
      cardBg.fillGradientStyle(0x1e3a8a, 0x1e3a8a, 0x3b82f6, 0x3b82f6, 1)
      cardBg.fillRoundedRect(-cardWidth/2, -cardHeight/2, cardWidth, cardHeight, 6)
      cardBg.lineStyle(2, 0x1e40af, 1)
      cardBg.strokeRoundedRect(-cardWidth/2, -cardHeight/2, cardWidth, cardHeight, 6)

      // 背面图案
      const pattern = this.scene.add.text(0, 0, '♠', {
        fontSize: isVertical ? '20px' : '28px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5)

      // 添加装饰边框
      const innerBorder = this.scene.add.graphics()
      innerBorder.lineStyle(1, 0x60a5fa, 0.8)
      innerBorder.strokeRoundedRect(-cardWidth/2 + 2, -cardHeight/2 + 2,
                                   cardWidth - 4, cardHeight - 4, 4)

      cardSprite.add([cardBg, innerBorder, pattern])
      cardSprite.playerId = player.id

      // 添加轻微的层次效果
      cardSprite.setDepth(i)

      this.cardSprites.set(`back_${player.id}_${i}`, cardSprite)
    }
  }

  // 清除指定玩家的卡牌
  clearPlayerCards(playerId) {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (sprite.playerId === playerId || key.startsWith(`back_${playerId}_`)) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示出牌区域的卡牌
  displayPlayedCards(cards, x, y) {
    // 清除之前的出牌
    this.clearPlayedCards()

    if (!cards || cards.length === 0) return

    const cardSpacing = 35
    const startX = x - (cards.length * cardSpacing) / 2

    // 添加出牌区域背景
    const playAreaBg = this.scene.add.graphics()
    playAreaBg.fillStyle(0x000000, 0.3)
    playAreaBg.fillRoundedRect(startX - 20, y - this.cardHeight/2 - 10,
                              cards.length * cardSpacing + 20, this.cardHeight + 20, 10)
    playAreaBg.lineStyle(2, 0xffffff, 0.5)
    playAreaBg.strokeRoundedRect(startX - 20, y - this.cardHeight/2 - 10,
                                cards.length * cardSpacing + 20, this.cardHeight + 20, 10)
    this.cardSprites.set('played_bg', playAreaBg)

    cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.85)
      // 添加轻微的阴影效果
      cardSprite.setDepth(10 + index)
      this.cardSprites.set(`played_${index}`, cardSprite)
    })
  }

  // 清除出牌区域
  clearPlayedCards() {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (key.startsWith('played_')) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示地主牌
  displayLandlordCards(cards, x, y) {
    // 清除之前的地主牌
    this.clearLandlordCards()

    if (!cards || cards.length === 0) return

    const cardSpacing = 35
    const startX = x - (cards.length * cardSpacing) / 2

    // 添加地主牌区域标识
    const landlordLabel = this.scene.add.text(x, y - 50, '地主牌', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 2,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5)
    this.cardSprites.set('landlord_label', landlordLabel)

    // 添加地主牌背景
    const landlordBg = this.scene.add.graphics()
    landlordBg.fillStyle(0xFFD700, 0.2)
    landlordBg.fillRoundedRect(startX - 15, y - this.cardHeight/2 - 5,
                              cards.length * cardSpacing + 10, this.cardHeight + 10, 8)
    landlordBg.lineStyle(2, 0xFFD700, 0.8)
    landlordBg.strokeRoundedRect(startX - 15, y - this.cardHeight/2 - 5,
                                cards.length * cardSpacing + 10, this.cardHeight + 10, 8)
    this.cardSprites.set('landlord_bg', landlordBg)

    cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.75)
      cardSprite.setDepth(20 + index)
      this.cardSprites.set(`landlord_${index}`, cardSprite)
    })
  }

  // 清除地主牌
  clearLandlordCards() {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (key.startsWith('landlord_')) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 获取选中的卡牌
  getSelectedCards() {
    return [...this.selectedCards]
  }

  // 销毁所有卡牌
  destroy() {
    this.cardSprites.forEach(sprite => sprite.destroy())
    this.cardSprites.clear()
    this.selectedCards = []
  }
}
